import { Blog } from "../../../../shared/types/Blog";

export type LearnNavParams = {
  LearnHomeScreen: undefined;
  BlogDetailsScreen: { blog: string };
  OnboardingTopics?: { fromManageScreen?: boolean };
  ManageStreakScreen?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  TopicsListScreen: { fromConnectScreen?: boolean };
  TopicQuestionsNavigator: { topicId: string; fromConnectScreen?: boolean };
};
